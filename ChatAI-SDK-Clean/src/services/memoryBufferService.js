const fetch = require('node-fetch');

/**
 * Memory Buffer Service for ChatAI-SDK-Clean
 * Handles conversation history retrieval and context formatting
 */
class MemoryBufferService {
  constructor() {
    this.userServiceBaseUrl = process.env.USER_SERVICE_URL || 'http://localhost:3000';
    this.defaultMaxMessages = 10;
    this.defaultMaxTokens = 2000;
  }

  /**
   * Get conversation history from User-Service
   * @param {string} conversationId - Conversation ID
   * @param {string} appId - Application ID
   * @param {number} maxMessages - Maximum messages to retrieve
   * @param {number} maxTokens - Maximum tokens for memory buffer
   * @returns {Object} Conversation history and metadata
   */
  async getConversationHistory(conversationId, appId, maxMessages = this.defaultMaxMessages, maxTokens = this.defaultMaxTokens) {
    try {
      console.log(`🧠 [MEMORY] Retrieving conversation history for conversationId: ${conversationId}`);
      
      const url = `${this.userServiceBaseUrl}/users/app/chatai/conversations/${conversationId}/memory-buffer`;
      const params = new URLSearchParams({
        appId: appId,
        maxMessages: maxMessages.toString(),
        maxTokens: maxTokens.toString()
      });

      const response = await fetch(`${url}?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': appId, // Use appId as API key for authentication
        },
        timeout: 5000, // 5 second timeout
      });

      if (!response.ok) {
        if (response.status === 404) {
          console.log(`🧠 [MEMORY] Conversation not found: ${conversationId}`);
          return { history: [], tokenCount: 0, messageCount: 0 };
        }
        throw new Error(`User-Service responded with status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.message || 'Failed to retrieve conversation history');
      }

      const result = data.result || { history: [], tokenCount: 0, messageCount: 0 };
      
      console.log(`🧠 [MEMORY] Retrieved ${result.history.length} messages (${result.tokenCount} tokens)`);
      
      return result;
    } catch (error) {
      console.error(`❌ [MEMORY] Failed to get conversation history: ${error.message}`);
      // Return empty history on error to allow conversation to continue
      return { history: [], tokenCount: 0, messageCount: 0 };
    }
  }

  /**
   * Store conversation message in User-Service
   * @param {string} conversationId - Conversation ID
   * @param {string} appId - Application ID
   * @param {string} question - User question
   * @param {string} response - AI response
   * @param {string} contextUsed - Context used for response
   * @param {Object} sourceReferences - Source references
   * @param {number} tokenCount - Token count
   * @returns {Object} Storage result
   */
  async storeConversationMessage(conversationId, appId, question, response, contextUsed = null, sourceReferences = null, tokenCount = null) {
    try {
      console.log(`🧠 [MEMORY] Storing conversation message for conversationId: ${conversationId}`);
      
      const url = `${this.userServiceBaseUrl}/users/app/chatai/conversations/${conversationId}/messages`;
      
      const payload = {
        appId: appId,
        conversationId: conversationId,
        question: question,
        response: response,
        contextUsed: contextUsed,
        sourceReferences: sourceReferences,
        tokenCount: tokenCount
      };

      const response_req = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': appId, // Use appId as API key for authentication
        },
        body: JSON.stringify(payload),
        timeout: 5000, // 5 second timeout
      });

      if (!response_req.ok) {
        throw new Error(`User-Service responded with status: ${response_req.status}`);
      }

      const data = await response_req.json();
      
      if (data.error) {
        throw new Error(data.message || 'Failed to store conversation message');
      }

      console.log(`🧠 [MEMORY] Message stored successfully with messageId: ${data.result?.messageId}`);
      
      return data.result;
    } catch (error) {
      console.error(`❌ [MEMORY] Failed to store conversation message: ${error.message}`);
      // Don't throw error - allow conversation to continue even if storage fails
      return { error: true, message: error.message };
    }
  }

  /**
   * Format context with conversation history for LLM
   * @param {string} vectorContext - Context from vector search
   * @param {Array} conversationHistory - Conversation history messages
   * @param {string} currentQuery - Current user query
   * @returns {string} Formatted context for LLM
   */
  formatContextWithHistory(vectorContext, conversationHistory, currentQuery) {
    let context = '';

    // Add conversation history if available
    if (conversationHistory && conversationHistory.length > 0) {
      context += `Previous Conversation:\n`;
      for (const msg of conversationHistory) {
        const role = msg.role === 'user' ? 'User' : 'Assistant';
        context += `${role}: ${msg.content}\n`;
      }
      context += `\n`;
    }

    // Add document context
    if (vectorContext && vectorContext.trim().length > 0) {
      context += `Document Context:\n${vectorContext}\n\n`;
    }

    // Add current query
    context += `Current Question: ${currentQuery}\n\n`;
    
    // Add instruction for the AI
    if (conversationHistory && conversationHistory.length > 0) {
      context += `Please answer the current question considering both the document context and the previous conversation history. Maintain conversation continuity and refer to previous exchanges when relevant.`;
    } else {
      context += `Please answer the question based on the provided document context.`;
    }

    return context;
  }

  /**
   * Estimate token count for text (rough approximation)
   * @param {string} text - Text to estimate tokens for
   * @returns {number} Estimated token count
   */
  estimateTokenCount(text) {
    if (!text) return 0;
    // Rough approximation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  /**
   * Check if conversation ID is provided and valid
   * @param {string} conversationId - Conversation ID to validate
   * @returns {boolean} Whether conversation ID is valid
   */
  isValidConversationId(conversationId) {
    return conversationId && 
           typeof conversationId === 'string' && 
           conversationId.trim().length > 0 &&
           conversationId !== 'null' &&
           conversationId !== 'undefined';
  }

  /**
   * Create a new conversation
   * @param {string} appId - Application ID
   * @param {string} title - Conversation title (optional)
   * @returns {Object} New conversation data
   */
  async createConversation(appId, title = null) {
    try {
      console.log(`🧠 [MEMORY] Creating new conversation for appId: ${appId}`);
      
      const url = `${this.userServiceBaseUrl}/users/app/chatai/conversations`;
      
      const payload = {
        appId: appId,
        title: title || 'New Conversation'
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': appId, // Use appId as API key for authentication
        },
        body: JSON.stringify(payload),
        timeout: 5000, // 5 second timeout
      });

      if (!response.ok) {
        throw new Error(`User-Service responded with status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.message || 'Failed to create conversation');
      }

      console.log(`🧠 [MEMORY] New conversation created with ID: ${data.result?.id}`);
      
      return data.result;
    } catch (error) {
      console.error(`❌ [MEMORY] Failed to create conversation: ${error.message}`);
      throw error;
    }
  }
}

module.exports = new MemoryBufferService();
