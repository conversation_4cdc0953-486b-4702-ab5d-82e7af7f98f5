const express = require('express');
const fetch = require('node-fetch');
const rateLimit = require('../middleware/rateLimit');
const { chatRateLimit } = require('../middleware/rateLimit');

const router = express.Router();

// Import vector processing routes
const vectorProcessingRoutes = require('./vectorProcessing');

// Apply general rate limiting to all routes
router.use(rateLimit);

// Apply stricter chat rate limiting to the main API endpoint
router.use('/api/v1/', chatRateLimit);

/**
 * Generate dynamic out-of-context response based on available documents
 * @param {Array} documents - Available documents from user service
 * @returns {string} Personalized out-of-context response
 */
function generateDynamicOutOfContextResponse(documents) {
  if (!documents || documents.length === 0) {
    return "I apologize, but I don't have any documents in my current knowledge base. Please upload some documents first, or contact support if you need assistance.";
  }

  // Extract document topics and titles
  const documentInfo = analyzeDocumentTopics(documents);

  if (documentInfo.topics.length === 0) {
    // Fallback to filenames if no topics detected
    const filenames = documents
      .map(doc => doc.filename || 'Unknown Document')
      .filter(name => name !== 'Unknown Document')
      .slice(0, 3); // Limit to first 3 for readability

    if (filenames.length > 0) {
      const fileList = filenames.length === 1
        ? filenames[0]
        : filenames.length === 2
          ? `${filenames[0]} and ${filenames[1]}`
          : `${filenames.slice(0, -1).join(', ')}, and ${filenames[filenames.length - 1]}`;

      return `I apologize, but I don't have information about that topic in my current knowledge base. Please ask questions related to ${fileList}, or contact support if you need assistance with other topics.`;
    }
  } else {
    // Use detected topics
    const topicList = documentInfo.topics.length === 1
      ? documentInfo.topics[0]
      : documentInfo.topics.length === 2
        ? `${documentInfo.topics[0]} and ${documentInfo.topics[1]}`
        : `${documentInfo.topics.slice(0, -1).join(', ')}, and ${documentInfo.topics[documentInfo.topics.length - 1]}`;

    return `I apologize, but I don't have information about that topic in my current knowledge base. Please ask questions related to ${topicList}, or contact support if you need assistance with other topics.`;
  }

  // Final fallback
  return "I apologize, but I don't have information about that topic in my current knowledge base. Please ask questions related to the documents that have been provided, or contact support if you need assistance with other topics.";
}

/**
 * Analyze documents to extract topics and themes
 * @param {Array} documents - Document objects
 * @returns {Object} Analysis results with topics and themes
 */
function analyzeDocumentTopics(documents) {
  const topics = new Set();
  const keywords = new Set();

  documents.forEach(doc => {
    // Extract from filename
    if (doc.filename) {
      const filenameTopics = extractTopicsFromFilename(doc.filename);
      filenameTopics.forEach(topic => topics.add(topic));
    }

    // Extract from document content if available
    if (doc.parsedData && doc.parsedData.text) {
      const contentTopics = extractTopicsFromContent(doc.parsedData.text);
      contentTopics.forEach(topic => topics.add(topic));
    }

    // Extract from metadata if available
    if (doc.metadata) {
      const metadataTopics = extractTopicsFromMetadata(doc.metadata);
      metadataTopics.forEach(topic => topics.add(topic));
    }
  });

  return {
    topics: Array.from(topics).slice(0, 3), // Limit to 3 most relevant topics
    keywords: Array.from(keywords)
  };
}

/**
 * Extract topics from filename
 * @param {string} filename - Document filename
 * @returns {Array} Extracted topics
 */
function extractTopicsFromFilename(filename) {
  const topics = [];

  // Remove file extension and clean up
  const cleanName = filename
    .replace(/\.[^/.]+$/, '')
    .replace(/[-_]/g, ' ')
    .toLowerCase();

  // Common topic patterns in filenames
  const topicPatterns = [
    // Technology topics
    { pattern: /machine\s*learning|ml/i, topic: 'machine learning' },
    { pattern: /artificial\s*intelligence|ai/i, topic: 'artificial intelligence' },
    { pattern: /deep\s*learning/i, topic: 'deep learning' },
    { pattern: /neural\s*network/i, topic: 'neural networks' },
    { pattern: /data\s*science/i, topic: 'data science' },
    { pattern: /python|programming/i, topic: 'programming' },
    { pattern: /algorithm/i, topic: 'algorithms' },

    // Business topics
    { pattern: /business|strategy/i, topic: 'business strategy' },
    { pattern: /marketing/i, topic: 'marketing' },
    { pattern: /finance|financial/i, topic: 'finance' },
    { pattern: /management/i, topic: 'management' },
    { pattern: /sales/i, topic: 'sales' },
    { pattern: /hr|human\s*resource|policy|policies/i, topic: 'HR policies' },

    // Academic topics
    { pattern: /research|study/i, topic: 'research' },
    { pattern: /analysis|analytics/i, topic: 'analysis' },
    { pattern: /report/i, topic: 'reports' },
    { pattern: /guide|tutorial/i, topic: 'guides and tutorials' },
    { pattern: /manual|documentation/i, topic: 'documentation' },

    // General topics
    { pattern: /health|medical/i, topic: 'health and medical information' },
    { pattern: /legal|law/i, topic: 'legal information' },
    { pattern: /education|learning/i, topic: 'education' },
    { pattern: /technology|tech/i, topic: 'technology' }
  ];

  // Check for topic patterns
  topicPatterns.forEach(({ pattern, topic }) => {
    if (pattern.test(cleanName)) {
      topics.push(topic);
    }
  });

  // If no specific topics found, use cleaned filename as topic
  if (topics.length === 0 && cleanName.length > 0) {
    // Convert to title case and use as topic
    const titleCase = cleanName.replace(/\b\w/g, l => l.toUpperCase());
    if (titleCase.length <= 50) { // Only if reasonable length
      topics.push(titleCase);
    }
  }

  return topics;
}

/**
 * Extract topics from document content (first 1000 characters)
 * @param {string} content - Document content
 * @returns {Array} Extracted topics
 */
function extractTopicsFromContent(content) {
  const topics = [];

  if (!content || content.length < 50) return topics;

  // Use first 1000 characters for topic detection
  const sample = content.substring(0, 1000).toLowerCase();

  // Topic detection patterns
  const contentPatterns = [
    { pattern: /machine\s+learning|ml\s+algorithm/i, topic: 'machine learning' },
    { pattern: /artificial\s+intelligence|ai\s+system/i, topic: 'artificial intelligence' },
    { pattern: /deep\s+learning|neural\s+network/i, topic: 'deep learning and neural networks' },
    { pattern: /data\s+science|data\s+analysis/i, topic: 'data science' },
    { pattern: /business\s+strategy|strategic\s+planning/i, topic: 'business strategy' },
    { pattern: /financial\s+analysis|finance/i, topic: 'financial analysis' },
    { pattern: /marketing\s+strategy|digital\s+marketing/i, topic: 'marketing' },
    { pattern: /software\s+development|programming/i, topic: 'software development' },
    { pattern: /project\s+management|management/i, topic: 'project management' },
    { pattern: /research\s+methodology|scientific\s+research/i, topic: 'research methodology' }
  ];

  contentPatterns.forEach(({ pattern, topic }) => {
    if (pattern.test(sample)) {
      topics.push(topic);
    }
  });

  return topics;
}

/**
 * Extract topics from document metadata
 * @param {Object} metadata - Document metadata
 * @returns {Array} Extracted topics
 */
function extractTopicsFromMetadata(metadata) {
  const topics = [];

  // Check common metadata fields
  const metadataFields = ['title', 'subject', 'description', 'keywords', 'category'];

  metadataFields.forEach(field => {
    if (metadata[field] && typeof metadata[field] === 'string') {
      const fieldTopics = extractTopicsFromFilename(metadata[field]);
      topics.push(...fieldTopics);
    }
  });

  return topics;
}

/**
 * Stream chat response
 */
async function streamChatResponse(res, query, context, sessionId, requestStartTime, cacheService, documentsLength = 0, conversationId = null, appId = null) {
  try {
    const openRouterService = require('../services/openRouterService');

    // Set headers for streaming
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');

    // Send initial session info
    res.write(`data: ${JSON.stringify({
      type: 'session',
      sessionId,
      timestamp: new Date().toISOString()
    })}\n\n`);

    // Check if no documents available - return static message
    if (documentsLength === 0) {
      const staticResponse = "Sorry, I don't have any information regarding this";
      const totalDuration = Date.now() - requestStartTime;

      // Send static response as content
      res.write(`data: ${JSON.stringify({
        type: 'content',
        content: staticResponse
      })}\n\n`);

      // Add to conversation history
      cacheService.addConversationEntry(sessionId, query, staticResponse, {
        contextLength: 0,
        totalDuration: totalDuration,
        staticResponse: true
      });

      // Send completion signal
      res.write(`data: ${JSON.stringify({
        type: 'done',
        timestamp: new Date().toISOString(),
        timing: { total: totalDuration }
      })}\n\n`);

      res.end();
      return;
    }

    // Stream response from OpenRouter
    const streamGenerator = openRouterService.generateStreamingResponse(query, context);
    let fullResponse = '';

    for await (const chunk of streamGenerator) {
      fullResponse += chunk;
      res.write(`data: ${JSON.stringify({
        type: 'content',
        content: chunk
      })}\n\n`);
    }

    const totalDuration = Date.now() - requestStartTime;

    // Add to conversation history (local cache)
    cacheService.addConversationEntry(sessionId, query, fullResponse, {
      contextLength: context.length,
      totalDuration: totalDuration
    });

    // Store in persistent conversation history if conversationId provided
    if (conversationId && appId) {
      console.log(`🧠 Storing streaming conversation message in User-Service...`);
      try {
        const memoryBufferService = require('../services/memoryBufferService');
        if (memoryBufferService.isValidConversationId(conversationId)) {
          const vectorContext = context.includes('Document Context:')
            ? context.split('Document Context:')[1].split('\n\nCurrent Question:')[0].trim()
            : context;

          await memoryBufferService.storeConversationMessage(
            conversationId,
            appId,
            query,
            fullResponse,
            vectorContext,
            null, // sourceReferences - could be enhanced later
            memoryBufferService.estimateTokenCount(query + fullResponse)
          );
          console.log(`🧠 Streaming conversation message stored successfully`);
        }
      } catch (error) {
        console.error(`❌ Failed to store streaming conversation message: ${error.message}`);
        // Continue - don't fail the request if storage fails
      }
    }

    // Send completion signal
    res.write(`data: ${JSON.stringify({
      type: 'done',
      timestamp: new Date().toISOString(),
      timing: { total: totalDuration },
      conversationId: conversationId || null
    })}\n\n`);

    res.end();

  } catch (error) {
    console.error('❌ Streaming error:', error.message);
    res.write(`data: ${JSON.stringify({
      type: 'error',
      message: 'An error occurred while generating the response'
    })}\n\n`);
    res.end();
  }
}

/**
 * Main API endpoint: /api/v1/
 * ONLY endpoint needed for ChatAI functionality
 * URL format: http://localhost:3002/api/v1/?apikey=test_api_key_1751884336144_vp9gospvg&query=invoice amount&conversationId=uuid
 */
router.get('/api/v1/', async (req, res) => {
  const { apikey, query, sessionId, conversationId, stream = 'true', testMode = 'false' } = req.query;

  // Test mode for vector database integration testing
  const isTestMode = testMode === 'true';
  if (isTestMode) {
    console.log(`\n🧪 ═══════════════════════════════════════════════════════════════`);
    console.log(`🧪 TEST MODE ACTIVATED - BYPASSING USER SERVICE`);
    console.log(`🧪 Query: "${query}"`);
    console.log(`🧪 ═══════════════════════════════════════════════════════════════\n`);
  }

  // Validate required parameters
  if (!apikey) {
    return res.status(400).json({
      error: true,
      message: 'API key is required'
    });
  }

  if (!query) {
    return res.status(400).json({
      error: true,
      message: 'Query is required'
    });
  }

  const streamBool = stream === 'true' || stream === true;

  try {
    const userService = require('../services/userService');
    const origin = req.headers.origin || req.headers.referer || 'unknown';

    // Start timing
    const requestStartTime = Date.now();
    console.log(`⏱️ [TIMING] Request started at: ${new Date().toISOString()}`);

    // Step 1: Check cached API key validation first (or use test mode)
    const cacheService = require('../services/cacheService');

    let validationResult;
    let userServiceDuration = 0;
    let appId, chatAiId;
    let chatAiData; // Declare chatAiData in broader scope

    if (isTestMode) {
      // Test mode: Use mock validation data
      console.log('🧪 Using test mode - mock validation data');
      validationResult = {
        appId: 'test-app-123',
        id: 'test-chatai-456',
        isValid: true,
        documents: [] // Add empty documents array for test mode
      };
      chatAiData = validationResult;
      appId = validationResult.appId;
      chatAiId = validationResult.id;
      console.log(`🧪 Mock validation: AppId: ${appId}, ChatAiId: ${chatAiId}`);
    } else {
      console.log('🔑 Checking API key validation cache...');

      validationResult = cacheService.getCachedApiKeyValidation(apikey);

      if (!validationResult) {
        // Cache miss - call User Service key-validator (which now includes documents!)
        console.log(`🔄 USER SERVICE CALL - CACHE MISS - API KEY VALIDATION`);

        const userServiceStartTime = Date.now();
        validationResult = await userService.validateApiKey(apikey, origin);
        userServiceDuration = Date.now() - userServiceStartTime;

        console.log(`✅ USER SERVICE SUCCESS: API key validation completed in ${userServiceDuration}ms`);

        // Cache the validation result
        cacheService.cacheApiKeyValidation(apikey, { result: validationResult });
      } else {
        console.log(`⚡ Using cached API key validation (saved ~200-500ms)`);
        validationResult = validationResult.result;
      }

      // Extract data from validation result
      chatAiData = validationResult;
      appId = chatAiData.appId;
      chatAiId = chatAiData.id;

      if (!appId || !chatAiId) {
        throw new Error('Invalid API key: missing appId or chatAiId');
      }

      console.log(`✅ API key validated successfully. AppId: ${appId}, ChatAiId: ${chatAiId}`);
    }

    // Step 2: Import required services
    const vectorSearchService = require('../services/vectorSearchService');
    const openRouterService = require('../services/openRouterService');

    // Step 3: Get or create session using appId
    const currentSessionId = cacheService.getOrCreateSession(appId, sessionId);

    // Step 4: Use documents from key-validator response (or mock data in test mode)
    let documents;

    if (isTestMode) {
      // Test mode: Use mock documents
      documents = [
        {
          id: 1,
          filename: 'chatai-integration-guide.txt',
          parsedText: 'ChatAI Vector Database Integration Guide. This comprehensive guide explains how to integrate Qdrant vector database with the ChatAI platform. The system supports semantic search, document retrieval, and tenant isolation using appId for secure multi-client deployments. Vector databases enable efficient similarity search using embeddings generated from document content.',
          appId: 'test-app-123'
        },
        {
          id: 2,
          filename: 'vector-search-documentation.txt',
          parsedText: 'Vector Search Documentation for ChatAI Platform. This document covers the implementation of vector search capabilities using Qdrant database. Features include automatic document chunking, embedding generation, similarity search, and context retrieval for AI chat responses. The system maintains strict tenant isolation to ensure data security.',
          appId: 'test-app-123'
        },
        {
          id: 3,
          filename: 'api-reference.txt',
          parsedText: 'ChatAI API Reference Documentation. This reference guide covers all API endpoints, authentication methods, and integration patterns. The main endpoint /api/v1/ supports query processing with vector database context retrieval. Parameters include apikey for authentication, query for user questions, and optional testMode for development.',
          appId: 'test-app-123'
        }
      ];
      console.log(`🧪 Using mock documents for testing: ${documents.length} documents available`);
    } else {
      documents = chatAiData.documents || [];
      console.log(`📄 Using documents from key-validator response: ${documents.length} documents available`);
      console.log(`🔍 DEBUG: chatAiData.documents = ${JSON.stringify(chatAiData.documents, null, 2)}`);
      console.log(`⚡ OPTIMIZATION: Saved 1 API call by including documents in key-validator response`);
    }

    // Cache the documents for future requests in this session
    cacheService.cacheDocuments(currentSessionId, appId, documents, null);

    // Step 5: Get context from Qdrant Vector Database using documents
    const vectorSearchStartTime = Date.now();
    let finalContext = '';
    let vectorSearchDuration = 0;

    console.log(`\n🔍 ═══════════════════════════════════════════════════════════════`);
    console.log(`🔍 STEP 5: VECTOR DATABASE CONTEXT RETRIEVAL`);
    console.log(`🔍 Documents available: ${documents.length}`);
    console.log(`🔍 AppId: ${appId}`);
    console.log(`🔍 Query: "${query}"`);
    console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

    if (documents.length > 0) {
      const cachedContext = cacheService.getCachedContext(currentSessionId, query);

      if (cachedContext) {
        finalContext = cachedContext;
        console.log(`⚡ Using cached context (saved ~1-3s)`);
        console.log(`📝 Cached context length: ${finalContext.length} characters`);
      } else {
        console.log(`🔍 Retrieving context from Qdrant Vector Database for ${documents.length} documents...`);
        console.log(`📄 Document details:`);
        documents.forEach((doc, index) => {
          console.log(`   ${index + 1}. ${doc.filename || 'Unknown'} (ID: ${doc.id})`);
        });

        finalContext = await vectorSearchService.retrieveFromMultipleDocuments(documents, query, appId);
        vectorSearchDuration = Date.now() - vectorSearchStartTime;

        // Cache the context
        cacheService.cacheContext(currentSessionId, query, finalContext);
        console.log(`✅ Context retrieved from vector database and cached in ${vectorSearchDuration}ms`);
        console.log(`📝 Final context length: ${finalContext.length} characters`);
      }
    } else {
      console.log(`⚠️ No documents available for context retrieval - returning static message`);
    }

    // Step 5.5: Get conversation history if conversationId is provided
    const memoryBufferService = require('../services/memoryBufferService');
    let conversationHistory = [];
    let conversationHistoryTokens = 0;

    if (memoryBufferService.isValidConversationId(conversationId)) {
      console.log(`\n🧠 ═══════════════════════════════════════════════════════════════`);
      console.log(`🧠 STEP 5.5: CONVERSATION HISTORY RETRIEVAL`);
      console.log(`🧠 ConversationId: ${conversationId}`);
      console.log(`🧠 AppId: ${appId}`);
      console.log(`🧠 ═══════════════════════════════════════════════════════════════\n`);

      try {
        const historyResult = await memoryBufferService.getConversationHistory(
          conversationId,
          appId,
          10, // maxMessages
          2000 // maxTokens
        );

        conversationHistory = historyResult.history || [];
        conversationHistoryTokens = historyResult.tokenCount || 0;

        console.log(`🧠 Retrieved ${conversationHistory.length} conversation messages (${conversationHistoryTokens} tokens)`);

        if (conversationHistory.length > 0) {
          // Update finalContext to include conversation history
          const vectorContext = finalContext;
          finalContext = memoryBufferService.formatContextWithHistory(
            vectorContext,
            conversationHistory,
            query
          );
          console.log(`🧠 Context enhanced with conversation history. New length: ${finalContext.length} characters`);
        }
      } catch (error) {
        console.error(`❌ Failed to retrieve conversation history: ${error.message}`);
        // Continue without conversation history
      }
    } else {
      console.log(`🧠 No conversationId provided - proceeding without conversation history`);
    }

    // Continue with existing logic for no documents case
    if (documents.length === 0) {

      // Return static message when no documents are available
      const staticResponse = "Sorry, I don't have any information regarding this";
      const totalDuration = Date.now() - requestStartTime;

      // Add to conversation history
      cacheService.addConversationEntry(currentSessionId, query, staticResponse, {
        documentsUsed: 0,
        contextLength: 0,
        cached: {
          apiKey: userServiceDuration === 0,
          context: false
        },
        staticResponse: true
      });

      console.log(`⏱️ [TIMING] Static response completed in: ${totalDuration}ms`);

      return res.json({
        error: false,
        sessionId: currentSessionId,
        response: staticResponse
      });
    }

    // Step 6: Generate response
    console.log('\n🔍 ═══════════════════════════════════════════════════════════════');
    console.log('🔍 FINAL CONTEXT BEING SENT TO OPENROUTER');
    console.log('🔍 ═══════════════════════════════════════════════════════════════');
    console.log(`📝 User Query: "${query}"`);
    console.log(`📄 Final Context Length: ${finalContext.length} characters`);
    console.log(`📊 Documents Used: ${documents.length}`);
    console.log('\n📋 FINAL CONTEXT CONTENT:');
    console.log('─'.repeat(80));
    console.log(finalContext);
    console.log('─'.repeat(80));
    console.log('🔍 ═══════════════════════════════════════════════════════════════\n');

    // Check if context is empty or insufficient (out-of-knowledge-base query)
    if (!finalContext || finalContext.trim().length === 0) {
      console.log(`⚠️ No relevant context found for query - this appears to be outside the knowledge base`);

      // Generate dynamic response based on available documents
      const outOfContextResponse = generateDynamicOutOfContextResponse(documents);
      const totalDuration = Date.now() - requestStartTime;

      // Add to conversation history (local cache)
      cacheService.addConversationEntry(currentSessionId, query, outOfContextResponse, {
        documentsUsed: documents.length,
        contextLength: 0,
        outOfContext: true,
        cached: {
          apiKey: userServiceDuration === 0,
          context: vectorSearchDuration === 0
        }
      });

      // Store in persistent conversation history if conversationId provided
      if (memoryBufferService.isValidConversationId(conversationId)) {
        console.log(`🧠 Storing out-of-context conversation message in User-Service...`);
        try {
          await memoryBufferService.storeConversationMessage(
            conversationId,
            appId,
            query,
            outOfContextResponse,
            'Out of context query - no relevant documents found',
            null, // sourceReferences
            memoryBufferService.estimateTokenCount(query + outOfContextResponse)
          );
          console.log(`🧠 Out-of-context conversation message stored successfully`);
        } catch (error) {
          console.error(`❌ Failed to store out-of-context conversation message: ${error.message}`);
          // Continue - don't fail the request if storage fails
        }
      }

      console.log(`⏱️ [TIMING] Out-of-context response completed in: ${totalDuration}ms`);

      if (streamBool) {
        // Handle streaming response for out-of-context
        res.write(`data: ${JSON.stringify({
          type: 'session',
          sessionId: currentSessionId,
          timestamp: new Date().toISOString()
        })}\n\n`);

        res.write(`data: ${JSON.stringify({
          type: 'content',
          content: outOfContextResponse
        })}\n\n`);

        res.write(`data: ${JSON.stringify({
          type: 'done',
          timestamp: new Date().toISOString(),
          timing: { total: totalDuration },
          outOfContext: true,
          conversationId: conversationId || null
        })}\n\n`);

        res.end();
        return;
      } else {
        return res.json({
          error: false,
          sessionId: currentSessionId,
          response: outOfContextResponse,
          outOfContext: true,
          conversationId: conversationId || null
        });
      }
    }

    // Context is available - proceed with normal response generation
    if (streamBool) {
      await streamChatResponse(res, query, finalContext, currentSessionId, requestStartTime, cacheService, documents.length, conversationId, appId);
    } else {
      const openRouterStartTime = Date.now();
      const response = await openRouterService.generateResponse(query, finalContext);
      const openRouterDuration = Date.now() - openRouterStartTime;

      // Add to conversation history (local cache)
      cacheService.addConversationEntry(currentSessionId, query, response, {
        documentsUsed: documents.length,
        contextLength: finalContext.length,
        cached: {
          apiKey: userServiceDuration === 0,
          context: vectorSearchDuration === 0
        }
      });

      // Store in persistent conversation history if conversationId provided
      if (memoryBufferService.isValidConversationId(conversationId)) {
        console.log(`🧠 Storing conversation message in User-Service...`);
        try {
          const vectorContext = finalContext.includes('Document Context:')
            ? finalContext.split('Document Context:')[1].split('\n\nCurrent Question:')[0].trim()
            : finalContext;

          await memoryBufferService.storeConversationMessage(
            conversationId,
            appId,
            query,
            response,
            vectorContext,
            null, // sourceReferences - could be enhanced later
            memoryBufferService.estimateTokenCount(query + response)
          );
          console.log(`🧠 Conversation message stored successfully`);
        } catch (error) {
          console.error(`❌ Failed to store conversation message: ${error.message}`);
          // Continue - don't fail the request if storage fails
        }
      }

      const totalDuration = Date.now() - requestStartTime;
      console.log(`⏱️ [TIMING] Total request completed in: ${totalDuration}ms`);

      res.json({
        error: false,
        sessionId: currentSessionId,
        response,
        conversationId: conversationId || null
      });
    }

  } catch (error) {
    console.error('❌ API v1 chat error:', error.message);

    // Return error in JSON format if headers not sent yet
    if (!res.headersSent) {
      const statusCode = error.message.includes('validation failed') ? 403 :
        error.message.includes('not found') ? 404 : 500;

      return res.status(statusCode).json({
        error: true,
        message: error.message
      });
    }
  }
});

// Test endpoint for vector search integration (without User Service dependency)
router.get('/test-vector', async (req, res) => {
  try {
    const { query = 'test query' } = req.query;
    const vectorSearchService = require('../services/vectorSearchService');

    // Mock documents for testing
    const mockDocuments = [
      {
        id: 1,
        filename: 'test-document.txt',
        parsedText: 'ChatAI Vector Database Integration Test Document. This document contains information about vector databases, semantic search, and ChatAI platform integration. Vector databases enable efficient similarity search using embeddings.',
        appId: 'test-app-123'
      },
      {
        id: 2,
        filename: 'integration-guide.txt',
        parsedText: 'Integration Guide for ChatAI Platform. This guide explains how to integrate vector databases with the ChatAI SDK. The system supports tenant isolation using appId for secure multi-client deployments.',
        appId: 'test-app-123'
      }
    ];

    console.log(`🧪 Testing vector search with query: "${query}"`);

    // Test vector search service
    const context = await vectorSearchService.retrieveFromMultipleDocuments(
      mockDocuments,
      query,
      'test-app-123'
    );

    // Get vector database stats
    const stats = await vectorSearchService.getStats();

    res.json({
      status: 'success',
      test: 'Vector Search Integration',
      query: query,
      documentsProcessed: mockDocuments.length,
      contextGenerated: {
        length: context.length,
        preview: context.substring(0, 200) + (context.length > 200 ? '...' : '')
      },
      vectorDatabase: {
        status: stats.status || 'not_available',
        stats: stats
      },
      integration: {
        service: 'Qdrant Vector Database',
        fallbackMode: stats.status === 'error',
        tenantIsolation: 'appId-based'
      }
    });

  } catch (error) {
    console.error('❌ Test vector endpoint error:', error.message);
    res.status(500).json({
      status: 'error',
      test: 'Vector Search Integration',
      error: error.message
    });
  }
});

// Health check endpoint
router.get('/health', async (req, res) => {
  try {
    const vectorSearchService = require('../services/vectorSearchService');

    // Check vector database status
    let vectorDbStatus = 'unknown';
    let vectorDbStats = {};

    try {
      vectorDbStats = await vectorSearchService.getStats();
      vectorDbStatus = vectorDbStats.status === 'green' ? 'healthy' : 'degraded';
    } catch (error) {
      vectorDbStatus = 'error';
      vectorDbStats = { error: error.message };
    }

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'ChatAI SDK Clean with Qdrant Vector Database',
      endpoints: {
        main: '/api/v1/?apikey=...&query=...',
        health: '/health'
      },
      vectorDatabase: {
        status: vectorDbStatus,
        stats: vectorDbStats
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      service: 'ChatAI SDK Clean',
      error: error.message
    });
  }
});

// Mount vector processing routes
router.use('/api/vector', vectorProcessingRoutes);

module.exports = router;
