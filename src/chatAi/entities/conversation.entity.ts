import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
} from 'typeorm';
import { ChatAi } from './chatAi.entity';
import { ChatAiMessage } from './message.entity';

@Entity('chat_ai_conversations')
export class ChatAiConversation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  title: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'timestamp', nullable: true })
  lastMessageAt: Date;

  @Column({ type: 'integer', default: 0 })
  messageCount: number;

  @Column({ type: 'varchar', nullable: false })
  userId: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @ManyToOne(() => ChatAi, (chatAi) => chatAi.conversations, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'chatAiId' })
  chatAi: ChatAi;

  @Column({ nullable: false })
  chatAiId: string;

  @OneToMany(() => ChatAiMessage, (message) => message.conversation, {
    cascade: true,
  })
  messages: ChatAiMessage[];
}
