import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  ManyToOne,
  CreateDateColumn,
  JoinColumn,
} from 'typeorm';
import { ChatAi } from './chatAi.entity';
import { ChatAiDocument } from './document.entity';
import { ChatAiConversation } from './conversation.entity';

@Entity('chat_ai_messages')
export class ChatAiMessage {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text', nullable: false })
  question: string;

  @Column({ type: 'text', nullable: false })
  response: string;

  @Column({ type: 'jsonb', nullable: true })
  sourceReferences: any; // array of page references from multiple docs

  @Column({ nullable: false, default: false })
  isAdminDebug: boolean; // Mark admin debug sessions

  @Column({ type: 'integer', nullable: false })
  messageOrder: number;

  @Column({ type: 'text', nullable: true })
  contextUsed: string;

  @Column({ type: 'integer', nullable: true })
  tokenCount: number;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  timestamp: Date;

  // Relationships
  @ManyToOne(() => ChatAi, (chatAi) => chatAi.messages, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'chatAiId' })
  chatAi: ChatAi;

  @Column({ nullable: false })
  chatAiId: string;

  @ManyToOne(() => ChatAiDocument, (document) => document.messages, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'documentId' })
  document: ChatAiDocument;

  @Column({ nullable: true })
  documentId: number; // optional - if question relates to specific doc

  @ManyToOne(
    () => ChatAiConversation,
    (conversation) => conversation.messages,
    {
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn({ name: 'conversationId' })
  conversation: ChatAiConversation;

  @Column({ type: 'uuid', nullable: true })
  conversationId: string;
}
