import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Query,
  Param,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ConversationService } from '../services/conversation.service';
import {
  CreateConversationDto,
  GetMemoryBufferDto,
  StoreConversationMessageDto,
  ListConversationsDto,
  UpdateConversationDto,
} from '../dto/conversation.dto';
import { User } from '../../user/entities/user.entity';

@ApiTags('ChatAI Conversations')
@Controller('users/app/chatai/conversations')
export class ConversationController {
  constructor(private readonly conversationService: ConversationService) {}

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post()
  @ApiOperation({ summary: 'Create a new conversation' })
  @ApiResponse({ status: 201, description: 'Conversation created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'ChatAI project not found' })
  async createConversation(
    @Body() createConversationDto: CreateConversationDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.conversationService.createConversation(userId, createConversationDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get()
  @ApiOperation({ summary: 'List conversations for a ChatAI project' })
  @ApiResponse({ status: 200, description: 'Conversations retrieved successfully' })
  @ApiResponse({ status: 404, description: 'ChatAI project not found' })
  async listConversations(
    @Query() listConversationsDto: ListConversationsDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.conversationService.listConversations(userId, listConversationsDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get(':conversationId/memory-buffer')
  @ApiOperation({ summary: 'Get memory buffer for conversation (for LLM context)' })
  @ApiResponse({ status: 200, description: 'Memory buffer retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  async getMemoryBuffer(
    @Param('conversationId') conversationId: string,
    @Query() getMemoryBufferDto: GetMemoryBufferDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    const dto = { ...getMemoryBufferDto, conversationId };
    return await this.conversationService.getMemoryBuffer(userId, dto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post(':conversationId/messages')
  @ApiOperation({ summary: 'Store a conversation message' })
  @ApiResponse({ status: 201, description: 'Message stored successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  async storeConversationMessage(
    @Param('conversationId') conversationId: string,
    @Body() storeMessageDto: StoreConversationMessageDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    const dto = { ...storeMessageDto, conversationId };
    return await this.conversationService.storeConversationMessage(userId, dto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Put(':conversationId')
  @ApiOperation({ summary: 'Update conversation details' })
  @ApiResponse({ status: 200, description: 'Conversation updated successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  async updateConversation(
    @Param('conversationId') conversationId: string,
    @Body() updateConversationDto: UpdateConversationDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.conversationService.updateConversation(userId, conversationId, updateConversationDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Delete(':conversationId')
  @ApiOperation({ summary: 'Delete conversation and all its messages' })
  @ApiResponse({ status: 200, description: 'Conversation deleted successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  async deleteConversation(
    @Param('conversationId') conversationId: string,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.conversationService.deleteConversation(userId, conversationId);
  }
}
