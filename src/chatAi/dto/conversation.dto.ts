import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsUUID, IsOptional, IsBoolean, IsNumber, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateConversationDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;

  @ApiPropertyOptional({ description: 'Conversation title' })
  @IsOptional()
  @IsString()
  title?: string;
}

export class GetMemoryBufferDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;

  @ApiProperty({ description: 'Conversation ID' })
  @IsUUID()
  conversationId: string;

  @ApiPropertyOptional({ description: 'Maximum messages to include', default: 10 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(20)
  maxMessages?: number = 10;

  @ApiPropertyOptional({ description: 'Maximum tokens for memory buffer', default: 2000 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(100)
  @Max(4000)
  maxTokens?: number = 2000;
}

export class StoreConversationMessageDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;

  @ApiProperty({ description: 'Conversation ID' })
  @IsUUID()
  conversationId: string;

  @ApiProperty({ description: 'User question' })
  @IsString()
  question: string;

  @ApiProperty({ description: 'AI response' })
  @IsString()
  response: string;

  @ApiPropertyOptional({ description: 'Context used for response' })
  @IsOptional()
  @IsString()
  contextUsed?: string;

  @ApiPropertyOptional({ description: 'Source references' })
  @IsOptional()
  sourceReferences?: any;

  @ApiPropertyOptional({ description: 'Token count' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  tokenCount?: number;
}

export class ListConversationsDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;

  @ApiPropertyOptional({ description: 'Page number for pagination', default: 1 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Number of conversations per page', default: 10 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(50)
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Search conversations by title' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: 'Filter by active status' })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isActive?: boolean;
}

export class UpdateConversationDto {
  @ApiPropertyOptional({ description: 'Conversation title' })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({ description: 'Active status' })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isActive?: boolean;
}
