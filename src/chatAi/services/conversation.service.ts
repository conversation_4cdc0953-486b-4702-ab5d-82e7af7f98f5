import { Injectable, HttpStatus, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { ChatAiConversation } from '../entities/conversation.entity';
import { ChatAiMessage } from '../entities/message.entity';
import { ChatAi } from '../entities/chatAi.entity';
import {
  CreateConversationDto,
  GetMemoryBufferDto,
  StoreConversationMessageDto,
  ListConversationsDto,
  UpdateConversationDto,
} from '../dto/conversation.dto';
import { CommonMessage, AppMessage } from '../../CommonMessages/CommonMessages';

@Injectable()
export class ConversationService {
  private readonly logger = new Logger(ConversationService.name);

  constructor(
    @InjectRepository(ChatAiConversation)
    private readonly conversationRepository: Repository<ChatAiConversation>,
    @InjectRepository(ChatAiMessage)
    private readonly messageRepository: Repository<ChatAiMessage>,
    @InjectRepository(ChatAi)
    private readonly chatAiRepository: Repository<ChatAi>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Create a new conversation
   */
  async createConversation(userId: number, dto: CreateConversationDto) {
    try {
      // Verify ChatAI project exists and belongs to user
      const chatAi = await this.chatAiRepository.findOne({
        where: {
          id: dto.appId,
          app: { user: { id: userId } },
        },
        relations: ['app', 'app.user'],
      });

      if (!chatAi) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: AppMessage.AppNotFound(),
        };
      }

      // Create conversation
      const conversation = this.conversationRepository.create({
        title: dto.title || 'New Conversation',
        userId: userId.toString(),
        chatAiId: dto.appId,
        chatAi: chatAi,
      });

      const savedConversation =
        await this.conversationRepository.save(conversation);

      return {
        error: false,
        statusCode: HttpStatus.CREATED,
        message: 'Conversation created successfully',
        result: {
          id: savedConversation.id,
          title: savedConversation.title,
          isActive: savedConversation.isActive,
          messageCount: savedConversation.messageCount,
          createdAt: savedConversation.createdAt,
        },
      };
    } catch (error) {
      this.logger.error(`Create conversation failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  /**
   * Get memory buffer for conversation (formatted for LLM)
   */
  async getMemoryBuffer(userId: number, dto: GetMemoryBufferDto) {
    try {
      // Verify conversation exists and belongs to user
      const conversation = await this.conversationRepository.findOne({
        where: {
          id: dto.conversationId,
          chatAiId: dto.appId,
          userId: userId.toString(),
        },
        relations: ['chatAi', 'chatAi.app', 'chatAi.app.user'],
      });

      if (!conversation) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Conversation not found or access denied',
        };
      }

      // Get recent messages for memory buffer
      const messages = await this.messageRepository.find({
        where: { conversationId: dto.conversationId },
        order: { messageOrder: 'ASC' },
        take: dto.maxMessages || 10,
      });

      // Format messages for LLM context
      const formattedHistory = [];
      let totalTokens = 0;

      for (const message of messages) {
        const userMessage = {
          role: 'user',
          content: message.question,
        };
        const assistantMessage = {
          role: 'assistant',
          content: message.response,
        };

        // Estimate tokens (rough approximation: 1 token ≈ 4 characters)
        const messageTokens = Math.ceil(
          (message.question.length + message.response.length) / 4,
        );

        if (totalTokens + messageTokens > (dto.maxTokens || 2000)) {
          break; // Stop if we exceed token limit
        }

        formattedHistory.push(userMessage, assistantMessage);
        totalTokens += messageTokens;
      }

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: 'Memory buffer retrieved successfully',
        result: {
          conversationId: dto.conversationId,
          history: formattedHistory,
          tokenCount: totalTokens,
          messageCount: messages.length,
        },
      };
    } catch (error) {
      this.logger.error(`Get memory buffer failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  /**
   * Store conversation message
   */
  async storeConversationMessage(
    userId: number,
    dto: StoreConversationMessageDto,
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Verify conversation exists and belongs to user
      const conversation = await queryRunner.manager.findOne(
        ChatAiConversation,
        {
          where: {
            id: dto.conversationId,
            chatAiId: dto.appId,
            userId: userId.toString(),
          },
          relations: ['chatAi'],
        },
      );

      if (!conversation) {
        await queryRunner.rollbackTransaction();
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Conversation not found or access denied',
        };
      }

      // Get next message order
      const lastMessage = await queryRunner.manager.findOne(ChatAiMessage, {
        where: { conversationId: dto.conversationId },
        order: { messageOrder: 'DESC' },
      });

      const nextOrder = lastMessage ? lastMessage.messageOrder + 1 : 1;

      // Create message
      const message = queryRunner.manager.create(ChatAiMessage, {
        question: dto.question,
        response: dto.response,
        contextUsed: dto.contextUsed,
        sourceReferences: dto.sourceReferences,
        tokenCount: dto.tokenCount,
        messageOrder: nextOrder,
        conversationId: dto.conversationId,
        chatAiId: dto.appId,
        conversation: conversation,
        chatAi: conversation.chatAi,
      });

      const savedMessage = await queryRunner.manager.save(message);

      // Update conversation metadata
      await queryRunner.manager.update(ChatAiConversation, dto.conversationId, {
        lastMessageAt: new Date(),
        messageCount: () => 'messageCount + 1',
      });

      await queryRunner.commitTransaction();

      return {
        error: false,
        statusCode: HttpStatus.CREATED,
        message: 'Message stored successfully',
        result: {
          messageId: savedMessage.id,
          messageOrder: savedMessage.messageOrder,
          conversationId: dto.conversationId,
        },
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Store conversation message failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * List conversations for user
   */
  async listConversations(userId: number, dto: ListConversationsDto) {
    try {
      // Verify ChatAI project exists and belongs to user
      const chatAi = await this.chatAiRepository.findOne({
        where: {
          id: dto.appId,
          app: { user: { id: userId } },
        },
        relations: ['app', 'app.user'],
      });

      if (!chatAi) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: AppMessage.AppNotFound(),
        };
      }

      // Build query
      const queryBuilder = this.conversationRepository
        .createQueryBuilder('conversation')
        .where('conversation.chatAiId = :appId', { appId: dto.appId })
        .andWhere('conversation.userId = :userId', {
          userId: userId.toString(),
        });

      // Apply filters
      if (dto.search) {
        queryBuilder.andWhere('conversation.title ILIKE :search', {
          search: `%${dto.search}%`,
        });
      }

      if (dto.isActive !== undefined) {
        queryBuilder.andWhere('conversation.isActive = :isActive', {
          isActive: dto.isActive,
        });
      }

      // Apply pagination
      const page = dto.page || 1;
      const limit = dto.limit || 10;
      const skip = (page - 1) * limit;

      queryBuilder
        .orderBy('conversation.lastMessageAt', 'DESC')
        .addOrderBy('conversation.createdAt', 'DESC')
        .skip(skip)
        .take(limit);

      const [conversations, totalCount] = await queryBuilder.getManyAndCount();

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message:
          totalCount > 0
            ? 'Conversations retrieved successfully'
            : 'No conversations found',
        result: conversations.map((conv) => ({
          id: conv.id,
          title: conv.title,
          isActive: conv.isActive,
          messageCount: conv.messageCount,
          lastMessageAt: conv.lastMessageAt,
          createdAt: conv.createdAt,
        })),
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalCount / limit),
          pageSize: limit,
          totalItems: totalCount,
          hasNextPage: skip + limit < totalCount,
          hasPreviousPage: page > 1,
        },
      };
    } catch (error) {
      this.logger.error(`List conversations failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  /**
   * Update conversation
   */
  async updateConversation(
    userId: number,
    conversationId: string,
    dto: UpdateConversationDto,
  ) {
    try {
      // Verify conversation exists and belongs to user
      const conversation = await this.conversationRepository.findOne({
        where: {
          id: conversationId,
          userId: userId.toString(),
        },
        relations: ['chatAi', 'chatAi.app', 'chatAi.app.user'],
      });

      if (!conversation) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Conversation not found or access denied',
        };
      }

      // Update conversation
      await this.conversationRepository.update(conversationId, {
        title: dto.title !== undefined ? dto.title : conversation.title,
        isActive:
          dto.isActive !== undefined ? dto.isActive : conversation.isActive,
      });

      const updatedConversation = await this.conversationRepository.findOne({
        where: { id: conversationId },
      });

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: 'Conversation updated successfully',
        result: {
          id: updatedConversation.id,
          title: updatedConversation.title,
          isActive: updatedConversation.isActive,
          messageCount: updatedConversation.messageCount,
          lastMessageAt: updatedConversation.lastMessageAt,
          updatedAt: updatedConversation.updatedAt,
        },
      };
    } catch (error) {
      this.logger.error(`Update conversation failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  /**
   * Delete conversation and all its messages
   */
  async deleteConversation(userId: number, conversationId: string) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Verify conversation exists and belongs to user
      const conversation = await queryRunner.manager.findOne(
        ChatAiConversation,
        {
          where: {
            id: conversationId,
            userId: userId.toString(),
          },
          relations: ['chatAi', 'chatAi.app', 'chatAi.app.user'],
        },
      );

      if (!conversation) {
        await queryRunner.rollbackTransaction();
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Conversation not found or access denied',
        };
      }

      // Delete all messages in conversation (cascade should handle this, but being explicit)
      await queryRunner.manager.delete(ChatAiMessage, { conversationId });

      // Delete conversation
      await queryRunner.manager.delete(ChatAiConversation, conversationId);

      await queryRunner.commitTransaction();

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: 'Conversation deleted successfully',
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Delete conversation failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    } finally {
      await queryRunner.release();
    }
  }
}
