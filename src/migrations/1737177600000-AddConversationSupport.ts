import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddConversationSupport1737177600000 implements MigrationInterface {
  name = 'AddConversationSupport1737177600000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create chat_ai_conversations table
    await queryRunner.query(`
      CREATE TABLE "chat_ai_conversations" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "title" character varying(255),
        "isActive" boolean NOT NULL DEFAULT true,
        "lastMessageAt" TIMESTAMP,
        "messageCount" integer NOT NULL DEFAULT 0,
        "userId" character varying NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "chatAiId" uuid NOT NULL,
        CONSTRAINT "PK_chat_ai_conversations" PRIMARY KEY ("id")
      )
    `);

    // Add new columns to chat_ai_messages
    await queryRunner.query(`
      ALTER TABLE "chat_ai_messages" 
      ADD COLUMN "conversationId" uuid,
      ADD COLUMN "messageOrder" integer NOT NULL DEFAULT 1,
      ADD COLUMN "contextUsed" text,
      ADD COLUMN "tokenCount" integer
    `);

    // Add foreign key constraint for conversations -> chatAi
    await queryRunner.query(`
      ALTER TABLE "chat_ai_conversations" 
      ADD CONSTRAINT "FK_chat_ai_conversations_chatAiId" 
      FOREIGN KEY ("chatAiId") REFERENCES "chat_ai_projects"("id") 
      ON DELETE CASCADE
    `);

    // Add foreign key constraint for messages -> conversations
    await queryRunner.query(`
      ALTER TABLE "chat_ai_messages" 
      ADD CONSTRAINT "FK_chat_ai_messages_conversationId" 
      FOREIGN KEY ("conversationId") REFERENCES "chat_ai_conversations"("id") 
      ON DELETE CASCADE
    `);

    // Add indexes for better performance
    await queryRunner.query(`
      CREATE INDEX "IDX_chat_ai_conversations_userId" 
      ON "chat_ai_conversations" ("userId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_chat_ai_conversations_chatAiId" 
      ON "chat_ai_conversations" ("chatAiId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_chat_ai_conversations_lastMessageAt" 
      ON "chat_ai_conversations" ("lastMessageAt")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_chat_ai_messages_conversationId" 
      ON "chat_ai_messages" ("conversationId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_chat_ai_messages_messageOrder" 
      ON "chat_ai_messages" ("conversationId", "messageOrder")
    `);

    // Add trigger to update updatedAt timestamp
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW."updatedAt" = now();
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);

    await queryRunner.query(`
      CREATE TRIGGER update_chat_ai_conversations_updated_at 
      BEFORE UPDATE ON "chat_ai_conversations" 
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop trigger and function
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS update_chat_ai_conversations_updated_at 
      ON "chat_ai_conversations"
    `);

    await queryRunner.query(`
      DROP FUNCTION IF EXISTS update_updated_at_column()
    `);

    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_chat_ai_messages_messageOrder"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_chat_ai_messages_conversationId"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_chat_ai_conversations_lastMessageAt"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_chat_ai_conversations_chatAiId"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_chat_ai_conversations_userId"`);

    // Drop foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "chat_ai_messages" 
      DROP CONSTRAINT IF EXISTS "FK_chat_ai_messages_conversationId"
    `);

    await queryRunner.query(`
      ALTER TABLE "chat_ai_conversations" 
      DROP CONSTRAINT IF EXISTS "FK_chat_ai_conversations_chatAiId"
    `);

    // Remove columns from chat_ai_messages
    await queryRunner.query(`
      ALTER TABLE "chat_ai_messages" 
      DROP COLUMN IF EXISTS "tokenCount",
      DROP COLUMN IF EXISTS "contextUsed",
      DROP COLUMN IF EXISTS "messageOrder",
      DROP COLUMN IF EXISTS "conversationId"
    `);

    // Drop chat_ai_conversations table
    await queryRunner.query(`DROP TABLE IF EXISTS "chat_ai_conversations"`);
  }
}
